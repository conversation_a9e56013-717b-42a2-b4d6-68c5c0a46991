<?php $__env->startSection('title', $profile->public_name . ' - Tutor Profesional Ngambiskuy'); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="<?php echo e($profile->description ?? 'Tutor profesional di Ngambiskuy dengan pengalaman mengajar ' . $stats['total_courses'] . ' kursus dan ' . $stats['total_students'] . ' siswa.'); ?>">
<meta name="keywords" content="tutor, <?php echo e($profile->public_name); ?>, kursus online, pembelajaran, <?php echo e($profile->user->skills ?? 'pendidikan'); ?>">
<meta property="og:title" content="<?php echo e($profile->public_name); ?> - Tutor Profesional Ngambiskuy">
<meta property="og:description" content="<?php echo e($profile->description ?? 'Tutor profesional dengan pengalaman mengajar di Ngambiskuy'); ?>">
<meta property="og:image" content="<?php echo e($profile->user->getProfilePictureUrl()); ?>">
<meta property="og:url" content="<?php echo e(request()->url()); ?>">
<meta name="twitter:card" content="summary_large_image">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Cover -->
<div class="relative bg-gradient-to-br from-primary to-primary-dark overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
        </svg>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="flex flex-col lg:flex-row items-center lg:items-start space-y-8 lg:space-y-0 lg:space-x-12">
            <!-- Profile Picture & Basic Info -->
            <div class="flex-shrink-0 text-center lg:text-left">
                <div class="relative inline-block">
                    <div class="w-32 h-32 lg:w-40 lg:h-40 rounded-full overflow-hidden border-4 border-white shadow-xl">
                        <img src="<?php echo e($profile->user->getProfilePictureUrl()); ?>"
                             alt="<?php echo e($profile->public_name); ?>"
                             class="w-full h-full object-cover">
                    </div>
                    <!-- Verification Badge -->
                    <div class="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2 shadow-lg">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                
                <!-- Mobile Stats -->
                <div class="lg:hidden mt-6 grid grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-white"><?php echo e($stats['total_courses']); ?></div>
                        <div class="text-white/80 text-sm">Kursus</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-white"><?php echo e(number_format($stats['total_students'])); ?></div>
                        <div class="text-white/80 text-sm">Siswa</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-white"><?php echo e(number_format($stats['average_rating'], 1)); ?></div>
                        <div class="text-white/80 text-sm">Rating</div>
                    </div>
                </div>
            </div>

            <!-- Main Info -->
            <div class="flex-1 text-center lg:text-left">
                <div class="mb-4">
                    <h1 class="text-4xl lg:text-5xl font-bold text-white mb-2"><?php echo e($profile->public_name); ?></h1>
                    <div class="flex flex-wrap items-center justify-center lg:justify-start gap-3 mb-4">
                        <?php if($profile->user->job_title): ?>
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                </svg>
                                <?php echo e($profile->user->job_title); ?>

                            </span>
                        <?php endif; ?>
                        
                        <?php if($profile->education_level): ?>
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                                <?php echo e($profile->education_level); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if($profile->description): ?>
                    <p class="text-xl text-white/90 leading-relaxed mb-6 max-w-3xl"><?php echo e($profile->description); ?></p>
                <?php endif; ?>

                <!-- Location & Company -->
                <div class="flex flex-wrap items-center justify-center lg:justify-start gap-6 mb-6 text-white/80">
                    <?php if($profile->user->location): ?>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <?php echo e($profile->user->location); ?>

                        </div>
                    <?php endif; ?>
                    
                    <?php if($profile->user->company): ?>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <?php echo e($profile->user->company); ?>

                        </div>
                    <?php endif; ?>
                    
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0a4 4 0 00-4 4v4a4 4 0 004 4h8a4 4 0 004-4v-4a4 4 0 00-4-4"></path>
                        </svg>
                        Bergabung <?php echo e($profile->created_at->format('M Y')); ?>

                    </div>
                </div>

                <!-- Desktop Stats -->
                <div class="hidden lg:grid grid-cols-4 gap-8 mb-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white"><?php echo e($stats['total_courses']); ?></div>
                        <div class="text-white/80">Kursus Tersedia</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white"><?php echo e(number_format($stats['total_students'])); ?></div>
                        <div class="text-white/80">Total Siswa</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white"><?php echo e(number_format($stats['average_rating'], 1)); ?></div>
                        <div class="text-white/80">Rating Rata-rata</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white"><?php echo e($stats['total_exams'] + $stats['total_blog_posts']); ?></div>
                        <div class="text-white/80">Konten Lainnya</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                       target="_blank"
                       class="inline-flex items-center justify-center px-8 py-3 bg-white text-primary font-semibold rounded-lg hover:bg-gray-50 transition-colors shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        Hubungi via WhatsApp
                    </a>
                    
                    <button onclick="shareProfile()" class="inline-flex items-center justify-center px-8 py-3 bg-white/20 text-white font-semibold rounded-lg hover:bg-white/30 transition-colors backdrop-blur-sm">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        Bagikan Profil
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Skills & Social Media Section -->
<div class="bg-white border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Skills -->
            <?php if($profile->user->getSkillsArray()): ?>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Keahlian</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php $__currentLoopData = $profile->user->getSkillsArray(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary border border-primary/20">
                                <?php echo e($skill); ?>

                            </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Social Media & Website -->
            <?php if($profile->user->hasSocialMediaLinks() || $profile->user->website): ?>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Temukan Saya Di</h3>
                    <div class="flex flex-wrap gap-3">
                        <?php $__currentLoopData = $profile->user->getSocialMediaLinks(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e($url); ?>" target="_blank" rel="noopener noreferrer"
                               class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                <?php if($platform === 'linkedin'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                    LinkedIn
                                <?php elseif($platform === 'github'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                    </svg>
                                    GitHub
                                <?php elseif($platform === 'twitter'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                    Twitter
                                <?php elseif($platform === 'instagram'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                    Instagram
                                <?php elseif($platform === 'youtube'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                    </svg>
                                    YouTube
                                <?php elseif($platform === 'facebook'): ?>
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                    Facebook
                                <?php endif; ?>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        <?php if($profile->user->website): ?>
                            <a href="<?php echo e($profile->user->website); ?>" target="_blank" rel="noopener noreferrer"
                               class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                </svg>
                                Website
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Enhanced Content Tabs -->
<div class="bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button onclick="showTab('overview')" id="overview-tab"
                        class="tab-button active whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Ringkasan
                </button>
                <button onclick="showTab('courses')" id="courses-tab"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    Kursus (<?php echo e($stats['total_courses']); ?>)
                </button>
                <button onclick="showTab('exams')" id="exams-tab"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Ujian (<?php echo e($stats['total_exams']); ?>)
                </button>
                <button onclick="showTab('blogs')" id="blogs-tab"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Blog (<?php echo e($stats['total_blog_posts']); ?>)
                </button>
            </nav>
        </div>
    </div>
</div>

<!-- Tab Content -->
<div class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <!-- Overview Tab -->
        <div id="overview-content" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- About Section -->
                    <?php if($profile->long_description || $profile->description): ?>
                        <div class="bg-white rounded-lg shadow-sm p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang <?php echo e($profile->public_name); ?></h2>
                            <?php if($profile->long_description): ?>
                                <div class="prose prose-gray max-w-none">
                                    <div class="text-gray-700 leading-relaxed whitespace-pre-line"><?php echo e($profile->long_description); ?></div>
                                </div>
                            <?php elseif($profile->description): ?>
                                <p class="text-gray-700 leading-relaxed"><?php echo e($profile->description); ?></p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Featured Courses -->
                    <?php if($courses->count() > 0): ?>
                        <div class="bg-white rounded-lg shadow-sm p-8">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl font-bold text-gray-900">Kursus Unggulan</h2>
                                <a href="#" onclick="showTab('courses')" class="text-primary hover:text-primary-dark font-medium">
                                    Lihat Semua →
                                </a>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <?php $__currentLoopData = $courses->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                        <div class="aspect-video bg-gray-100 relative">
                                            <?php if($course->thumbnail): ?>
                                                <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                                     alt="<?php echo e($course->title); ?>"
                                                     class="w-full h-full object-cover">
                                            <?php else: ?>
                                                <div class="w-full h-full flex items-center justify-center">
                                                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                    </svg>
                                                </div>
                                            <?php endif; ?>
                                            <!-- Price Badge -->
                                            <div class="absolute top-3 right-3">
                                                <?php if($course->is_free): ?>
                                                    <span class="bg-green-500 text-white px-2 py-1 rounded text-sm font-medium">Gratis</span>
                                                <?php else: ?>
                                                    <span class="bg-primary text-white px-2 py-1 rounded text-sm font-medium">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="p-4">
                                            <div class="flex items-center text-sm text-gray-500 mb-2">
                                                <?php if($course->category): ?>
                                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs"><?php echo e($course->category->name); ?></span>
                                                <?php endif; ?>
                                                <span class="ml-auto"><?php echo e($course->level); ?></span>
                                            </div>
                                            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($course->title); ?></h3>
                                            <p class="text-gray-600 text-sm mb-3 line-clamp-2"><?php echo e($course->description); ?></p>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <div class="flex text-yellow-400">
                                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                                            <?php if($i <= floor($course->average_rating)): ?>
                                                                <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                                </svg>
                                                            <?php else: ?>
                                                                <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                                </svg>
                                                            <?php endif; ?>
                                                        <?php endfor; ?>
                                                    </div>
                                                    <span class="ml-1 text-sm"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                                </div>
                                                <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                                    Lihat Detail →
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Stats -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistik Tutor</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Total Kursus</span>
                                <span class="font-semibold text-gray-900"><?php echo e($stats['total_courses']); ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Total Siswa</span>
                                <span class="font-semibold text-gray-900"><?php echo e(number_format($stats['total_students'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Rating Rata-rata</span>
                                <div class="flex items-center">
                                    <span class="font-semibold text-gray-900 mr-1"><?php echo e(number_format($stats['average_rating'], 1)); ?></span>
                                    <div class="flex">
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <svg class="w-3 h-3 <?php echo e($i <= floor($stats['average_rating']) ? 'text-yellow-400' : 'text-gray-300'); ?> fill-current" viewBox="0 0 20 20">
                                                <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                            </svg>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Total Ujian</span>
                                <span class="font-semibold text-gray-900"><?php echo e($stats['total_exams']); ?></span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Artikel Blog</span>
                                <span class="font-semibold text-gray-900"><?php echo e($stats['total_blog_posts']); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Card -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Hubungi Tutor</h3>
                        <div class="space-y-3">
                            <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                               target="_blank"
                               class="w-full btn btn-primary text-center block">
                                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                                </svg>
                                WhatsApp
                            </a>
                            <button onclick="shareProfile()" class="w-full btn btn-outline text-center">
                                <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                </svg>
                                Bagikan Profil
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Courses Tab -->
        <div id="courses-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Semua Kursus dari <?php echo e($profile->public_name); ?></h2>

                <?php if($courses->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="aspect-video bg-gray-100 relative">
                                    <?php if($course->thumbnail): ?>
                                        <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                             alt="<?php echo e($course->title); ?>"
                                             class="w-full h-full object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-full flex items-center justify-center">
                                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>
                                    <!-- Price Badge -->
                                    <div class="absolute top-3 right-3">
                                        <?php if($course->is_free): ?>
                                            <span class="bg-green-500 text-white px-2 py-1 rounded text-sm font-medium">Gratis</span>
                                        <?php else: ?>
                                            <span class="bg-primary text-white px-2 py-1 rounded text-sm font-medium">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="p-4">
                                    <div class="flex items-center text-sm text-gray-500 mb-2">
                                        <?php if($course->category): ?>
                                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs"><?php echo e($course->category->name); ?></span>
                                        <?php endif; ?>
                                        <span class="ml-auto"><?php echo e($course->level); ?></span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($course->title); ?></h3>
                                    <p class="text-gray-600 text-sm mb-3 line-clamp-2"><?php echo e($course->description); ?></p>
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                        <span><?php echo e($course->total_lessons); ?> pelajaran</span>
                                        <span><?php echo e($course->total_duration_minutes); ?> menit</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex text-yellow-400">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= floor($course->average_rating)): ?>
                                                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    <?php else: ?>
                                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="ml-1 text-sm"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                        </div>
                                        <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                            Lihat Detail →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if($stats['total_courses'] > 6): ?>
                        <div class="text-center mt-8">
                            <a href="<?php echo e(route('courses.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn btn-primary">
                                Lihat Semua Kursus (<?php echo e($stats['total_courses']); ?>)
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Kursus</h3>
                        <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat kursus. Pantau terus untuk update terbaru!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Exams Tab -->
        <div id="exams-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Ujian dari <?php echo e($profile->public_name); ?></h2>

                <?php if($exams->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <?php if($exam->category): ?>
                                            <span class="bg-primary/10 text-primary px-2 py-1 rounded text-xs font-medium"><?php echo e($exam->category->name); ?></span>
                                        <?php endif; ?>
                                        <span class="bg-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-100 text-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-800 px-2 py-1 rounded text-xs font-medium">
                                            <?php echo e(ucfirst($exam->difficulty_level)); ?>

                                        </span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($exam->title); ?></h3>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($exam->description); ?></p>
                                    <div class="space-y-2 mb-4">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <?php echo e($exam->time_limit); ?> menit
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <?php echo e($exam->total_questions); ?> soal
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Passing score: <?php echo e($exam->passing_score); ?>%
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="text-lg font-bold text-primary">
                                            <?php if($exam->price > 0): ?>
                                                Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?>

                                            <?php else: ?>
                                                Gratis
                                            <?php endif; ?>
                                        </div>
                                        <a href="<?php echo e(route('exams.show', $exam)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                            Lihat Detail →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if($stats['total_exams'] > 6): ?>
                        <div class="text-center mt-8">
                            <a href="<?php echo e(route('exams.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn btn-primary">
                                Lihat Semua Ujian (<?php echo e($stats['total_exams']); ?>)
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                        <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat ujian. Pantau terus untuk update terbaru!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Blogs Tab -->
        <div id="blogs-content" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-sm p-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Blog dari <?php echo e($profile->public_name); ?></h2>

                <?php if($blogPosts->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php $__currentLoopData = $blogPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <article class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
                                <?php if($post->featured_image): ?>
                                    <div class="aspect-video bg-gray-100">
                                        <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>"
                                             alt="<?php echo e($post->title); ?>"
                                             class="w-full h-full object-cover">
                                    </div>
                                <?php endif; ?>
                                <div class="p-6">
                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                        <?php if($post->category): ?>
                                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs mr-3"><?php echo e($post->category->name); ?></span>
                                        <?php endif; ?>
                                        <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>">
                                            <?php echo e($post->published_at->format('d M Y')); ?>

                                        </time>
                                        <span class="mx-2">•</span>
                                        <span><?php echo e($post->read_time); ?> menit baca</span>
                                    </div>
                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                                        <a href="<?php echo e(route('blog.show', $post)); ?>" class="hover:text-primary transition-colors">
                                            <?php echo e($post->title); ?>

                                        </a>
                                    </h3>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center text-sm text-gray-500">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            <?php echo e($post->views_count); ?> views
                                        </div>
                                        <a href="<?php echo e(route('blog.show', $post)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                            Baca Selengkapnya →
                                        </a>
                                    </div>
                                </div>
                            </article>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <?php if($stats['total_blog_posts'] > 6): ?>
                        <div class="text-center mt-8">
                            <a href="<?php echo e(route('blog.index')); ?>?author=<?php echo e($profile->user->id); ?>" class="btn btn-primary">
                                Lihat Semua Blog (<?php echo e($stats['total_blog_posts']); ?>)
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Blog</h3>
                        <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum menulis blog. Pantau terus untuk update terbaru!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-8">
            <a href="<?php echo e(route('home')); ?>" class="text-primary hover:text-primary-dark font-medium">
                ← Kembali ke Beranda
            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .tab-button {
        @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300;
    }

    .tab-button.active {
        @apply border-primary text-primary;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .aspect-video {
        aspect-ratio: 16 / 9;
    }

    /* Hero gradient animation */
    .bg-gradient-to-br {
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Smooth transitions */
    .transition-all {
        transition: all 0.3s ease;
    }

    /* Backdrop blur support */
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    const selectedContent = document.getElementById(tabName + '-content');
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }

    // Add active class to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }

    // Update URL hash without scrolling
    if (history.pushState) {
        history.pushState(null, null, '#' + tabName);
    }
}

function shareProfile() {
    if (navigator.share) {
        navigator.share({
            title: '<?php echo e($profile->public_name); ?> - Tutor Ngambiskuy',
            text: '<?php echo e($profile->description ?? "Tutor profesional di Ngambiskuy"); ?>',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            // Show toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = 'Link profil berhasil disalin!';
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        });
    }
}

// Initialize the first tab as active on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in URL
    const hash = window.location.hash.substring(1);
    const validTabs = ['overview', 'courses', 'exams', 'blogs'];

    if (hash && validTabs.includes(hash)) {
        showTab(hash);
    } else {
        showTab('overview');
    }
});

// Handle browser back/forward buttons
window.addEventListener('popstate', function() {
    const hash = window.location.hash.substring(1);
    const validTabs = ['overview', 'courses', 'exams', 'blogs'];

    if (hash && validTabs.includes(hash)) {
        showTab(hash);
    } else {
        showTab('overview');
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/tutor/public-profile-v2.blade.php ENDPATH**/ ?>