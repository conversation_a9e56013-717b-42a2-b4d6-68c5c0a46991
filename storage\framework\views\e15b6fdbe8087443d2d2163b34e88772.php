<?php $__env->startSection('title', $profile->public_name . ' - Tu<PERSON> | Ngambiskuy'); ?>

<?php $__env->startSection('meta'); ?>
<meta name="description" content="Profil tutor <?php echo e($profile->public_name); ?> di Ngambiskuy. <?php echo e($stats['total_courses']); ?> kursus, <?php echo e($stats['total_students']); ?> siswa, rating <?php echo e(number_format($stats['average_rating'], 1)); ?>/5.0">
<meta name="keywords" content="tutor <?php echo e($profile->public_name); ?>, kursus online, pembelajaran teknologi, ngambiskuy">
<meta property="og:title" content="<?php echo e($profile->public_name); ?> - Tutor Professional | Ngambiskuy">
<meta property="og:description" content="Bergabung dengan <?php echo e($stats['total_students']); ?> siswa yang telah belajar dari <?php echo e($profile->public_name); ?>">
<meta property="og:image" content="<?php echo e($profile->user->getProfilePictureUrl()); ?>">
<meta property="og:url" content="<?php echo e(route('tutor.public-profile', $profile->public_name_slug)); ?>">
<?php $__env->stopSection(); ?>

<!-- Structured Data for SEO -->
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Person",
  "name": "<?php echo e($profile->public_name); ?>",
  "description": "<?php echo e($profile->description); ?>",
  "image": "<?php echo e($profile->user->getProfilePictureUrl()); ?>",
  "jobTitle": "<?php echo e($profile->user->job_title ?? 'Tutor Professional'); ?>",
  "worksFor": {
    "@type": "Organization",
    "name": "Ngambiskuy"
  },
  "url": "<?php echo e(route('tutor.public-profile', $profile->public_name_slug)); ?>",
  "sameAs": [
    <?php if($profile->user->website): ?>"<?php echo e($profile->user->website); ?>",<?php endif; ?>
    <?php $__currentLoopData = $profile->user->getSocialMediaLinks(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    "<?php echo e($url); ?>"<?php if(!$loop->last): ?>,<?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  ],
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": <?php echo e(number_format($stats['average_rating'], 1)); ?>,
    "ratingCount": <?php echo e($stats['total_students']); ?>,
    "bestRating": 5,
    "worstRating": 1
  },
  "teaches": [
    <?php $__currentLoopData = $courses->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    {
      "@type": "Course",
      "name": "<?php echo e($course->title); ?>",
      "description": "<?php echo e($course->description); ?>",
      "provider": {
        "@type": "Organization",
        "name": "Ngambiskuy"
      }
    }<?php if(!$loop->last): ?>,<?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  ]
}
</script>

<style>
/* Custom styles for professional tutor profile */
.hero-gradient {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFB347 100%);
}

.stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.tab-button {
    @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all duration-200;
}

.tab-button.active {
    @apply border-primary text-primary bg-primary/5;
}

.content-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: #FF6B35;
}

.achievement-badge {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.social-link {
    transition: all 0.3s ease;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Professional rating stars */
.rating-star {
    transition: all 0.2s ease;
}

.rating-star.filled {
    color: #FFC107;
    filter: drop-shadow(0 1px 2px rgba(255, 193, 7, 0.3));
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .hero-content {
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}
</style>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="hero-gradient relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>'); background-size: 60px 60px;"></div>
    </div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="grid lg:grid-cols-3 gap-8 items-center">
            <!-- Profile Info -->
            <div class="lg:col-span-2 hero-content">
                <div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                    <!-- Profile Picture -->
                    <div class="relative">
                        <div class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl">
                            <img src="<?php echo e($profile->user->getProfilePictureUrl()); ?>"
                                 alt="<?php echo e($profile->public_name); ?>"
                                 class="w-full h-full object-cover">
                        </div>
                        <!-- Verification Badge -->
                        <div class="absolute -bottom-2 -right-2 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center border-4 border-white shadow-lg">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- Profile Details -->
                    <div class="flex-1 text-white">
                        <div class="mb-4">
                            <h1 class="text-4xl lg:text-5xl font-bold mb-2"><?php echo e($profile->public_name); ?></h1>
                            <?php if($profile->user->job_title): ?>
                                <p class="text-xl text-white/90 mb-2"><?php echo e($profile->user->job_title); ?></p>
                            <?php endif; ?>
                            <div class="flex flex-wrap items-center gap-4 text-white/80">
                                <?php if($profile->user->location): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <?php echo e($profile->user->location); ?>

                                    </div>
                                <?php endif; ?>
                                <?php if($profile->user->company): ?>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <?php echo e($profile->user->company); ?>

                                    </div>
                                <?php endif; ?>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Bergabung <?php echo e($profile->created_at->format('M Y')); ?>

                                </div>
                            </div>
                        </div>

                        <?php if($profile->description): ?>
                            <p class="text-lg text-white/90 leading-relaxed mb-6"><?php echo e($profile->description); ?></p>
                        <?php endif; ?>

                        <!-- Rating and Reviews -->
                        <div class="flex items-center space-x-6 mb-6">
                            <div class="flex items-center">
                                <div class="flex mr-2">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <svg class="w-5 h-5 rating-star <?php echo e($i <= floor($stats['average_rating']) ? 'filled' : 'text-white/30'); ?>" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    <?php endfor; ?>
                                </div>
                                <span class="text-white font-semibold text-lg"><?php echo e(number_format($stats['average_rating'], 1)); ?></span>
                                <span class="text-white/70 ml-2">(<?php echo e($stats['total_students']); ?> ulasan)</span>
                            </div>
                        </div>

                        <!-- Skills Tags -->
                        <?php if($profile->user->getSkillsArray()): ?>
                            <div class="flex flex-wrap gap-2">
                                <?php $__currentLoopData = $profile->user->getSkillsArray(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white border border-white/30">
                                        <?php echo e($skill); ?>

                                    </span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="lg:col-span-1">
                <div class="stats-grid grid grid-cols-2 lg:grid-cols-1 gap-4">
                    <div class="stats-card rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-primary mb-1"><?php echo e($stats['total_students']); ?></div>
                        <div class="text-gray-600 text-sm">Total Siswa</div>
                    </div>
                    <div class="stats-card rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-primary mb-1"><?php echo e($stats['total_courses']); ?></div>
                        <div class="text-gray-600 text-sm">Kursus Aktif</div>
                    </div>
                    <div class="stats-card rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-primary mb-1"><?php echo e($stats['total_exams']); ?></div>
                        <div class="text-gray-600 text-sm">Ujian Tersedia</div>
                    </div>
                    <div class="stats-card rounded-xl p-6 text-center">
                        <div class="text-3xl font-bold text-primary mb-1"><?php echo e($stats['total_blog_posts']); ?></div>
                        <div class="text-gray-600 text-sm">Artikel Blog</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Action Bar -->
<section class="bg-white border-b border-gray-200 sticky top-0 z-40 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between py-4">
            <!-- Navigation Tabs -->
            <nav class="flex space-x-8" aria-label="Tabs">
                <button onclick="showTab('overview')" id="overview-tab" class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Ringkasan
                </button>
                <button onclick="showTab('courses')" id="courses-tab" class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    Kursus (<?php echo e($stats['total_courses']); ?>)
                </button>
                <button onclick="showTab('exams')" id="exams-tab" class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Ujian (<?php echo e($stats['total_exams']); ?>)
                </button>
                <button onclick="showTab('blogs')" id="blogs-tab" class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Blog (<?php echo e($stats['total_blog_posts']); ?>)
                </button>
            </nav>

            <!-- Contact Actions -->
            <div class="flex items-center space-x-3">
                <!-- Social Media Links -->
                <?php if($profile->user->hasSocialMediaLinks() || $profile->user->website): ?>
                    <div class="flex items-center space-x-2">
                        <?php $__currentLoopData = $profile->user->getSocialMediaLinks(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e($url); ?>" target="_blank" rel="noopener noreferrer"
                               class="social-link w-8 h-8 rounded-full bg-gray-100 hover:bg-primary hover:text-white flex items-center justify-center transition-all duration-200">
                                <?php if($platform === 'linkedin'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                <?php elseif($platform === 'github'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                    </svg>
                                <?php elseif($platform === 'twitter'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                <?php elseif($platform === 'instagram'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                <?php elseif($platform === 'youtube'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                                    </svg>
                                <?php elseif($platform === 'facebook'): ?>
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                    </svg>
                                <?php endif; ?>
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php if($profile->user->website): ?>
                            <a href="<?php echo e($profile->user->website); ?>" target="_blank" rel="noopener noreferrer"
                               class="social-link w-8 h-8 rounded-full bg-gray-100 hover:bg-primary hover:text-white flex items-center justify-center transition-all duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                </svg>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Contact Button -->
                <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                   target="_blank"
                   class="btn btn-primary px-6 py-2 text-sm font-medium">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                    Hubungi Tutor
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Overview Tab -->
    <div id="overview-content" class="tab-content">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- About Section -->
                <?php if($profile->long_description || $profile->description): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Tentang <?php echo e($profile->public_name); ?></h2>

                    <?php if($profile->long_description): ?>
                        <div class="prose prose-gray max-w-none">
                            <div class="text-gray-700 leading-relaxed whitespace-pre-line"><?php echo e($profile->long_description); ?></div>
                        </div>
                    <?php elseif($profile->description): ?>
                        <div class="prose prose-gray max-w-none">
                            <p class="text-gray-700 leading-relaxed"><?php echo e($profile->description); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Education Info -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Latar Belakang Pendidikan</h3>
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900"><?php echo e($profile->education_level); ?></p>
                                <p class="text-sm text-gray-500">Tingkat Pendidikan</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Featured Courses -->
                <?php if($courses->count() > 0): ?>
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Kursus Unggulan</h2>
                        <?php if($stats['total_courses'] > 3): ?>
                            <button onclick="showTab('courses')" class="text-primary hover:text-primary-dark font-medium text-sm">
                                Lihat Semua (<?php echo e($stats['total_courses']); ?>) →
                            </button>
                        <?php endif; ?>
                    </div>

                    <div class="grid md:grid-cols-2 gap-6">
                        <?php $__currentLoopData = $courses->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="content-card rounded-lg overflow-hidden">
                                <div class="aspect-video bg-gray-100 relative">
                                    <?php if($course->thumbnail): ?>
                                        <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                             alt="<?php echo e($course->title); ?>"
                                             class="w-full h-full object-cover">
                                    <?php else: ?>
                                        <div class="w-full h-full flex items-center justify-center">
                                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Price Badge -->
                                    <div class="absolute top-3 right-3">
                                        <?php if($course->is_free): ?>
                                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Gratis</span>
                                        <?php else: ?>
                                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-3">
                                        <?php if($course->category): ?>
                                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium"><?php echo e($course->category->name); ?></span>
                                        <?php endif; ?>
                                        <span class="text-sm text-gray-500"><?php echo e($course->level); ?></span>
                                    </div>

                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($course->title); ?></h3>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        <span><?php echo e($course->total_lessons); ?> pelajaran</span>
                                        <span><?php echo e($course->total_duration_minutes); ?> menit</span>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex text-yellow-400 mr-2">
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <?php if($i <= floor($course->average_rating)): ?>
                                                        <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    <?php else: ?>
                                                        <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                        </svg>
                                                    <?php endif; ?>
                                                <?php endfor; ?>
                                            </div>
                                            <span class="text-sm"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                        </div>

                                        <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                            Lihat Detail →
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Achievement Badges -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pencapaian</h3>
                    <div class="space-y-3">
                        <?php if($stats['total_students'] >= 100): ?>
                            <div class="flex items-center space-x-3">
                                <div class="achievement-badge w-10 h-10 rounded-full flex items-center justify-center text-white">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Tutor Populer</p>
                                    <p class="text-sm text-gray-500">100+ siswa</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($stats['total_courses'] >= 5): ?>
                            <div class="flex items-center space-x-3">
                                <div class="achievement-badge w-10 h-10 rounded-full flex items-center justify-center text-white">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Penulis Produktif</p>
                                    <p class="text-sm text-gray-500">5+ kursus</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if($stats['average_rating'] >= 4.5): ?>
                            <div class="flex items-center space-x-3">
                                <div class="achievement-badge w-10 h-10 rounded-full flex items-center justify-center text-white">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Tutor Berkualitas</p>
                                    <p class="text-sm text-gray-500">Rating 4.5+</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center space-x-3">
                            <div class="achievement-badge w-10 h-10 rounded-full flex items-center justify-center text-white">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900">Tutor Terverifikasi</p>
                                <p class="text-sm text-gray-500">Ngambiskuy Certified</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistik Cepat</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Respons Rate</span>
                            <span class="font-semibold text-green-600">98%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Waktu Respons</span>
                            <span class="font-semibold text-gray-900">< 2 jam</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Tingkat Penyelesaian</span>
                            <span class="font-semibold text-green-600">95%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Bergabung Sejak</span>
                            <span class="font-semibold text-gray-900"><?php echo e($profile->created_at->format('M Y')); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Hubungi Saya</h3>
                    <div class="space-y-4">
                        <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
                           target="_blank"
                           class="w-full btn btn-primary text-center block">
                            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                            </svg>
                            Chat WhatsApp
                        </a>

                        <div class="text-center text-sm text-gray-500">
                            Atau hubungi melalui platform lain
                        </div>

                        <?php if($profile->user->hasSocialMediaLinks()): ?>
                            <div class="flex justify-center space-x-3">
                                <?php $__currentLoopData = $profile->user->getSocialMediaLinks(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="<?php echo e($url); ?>" target="_blank" rel="noopener noreferrer"
                                       class="social-link w-10 h-10 rounded-full bg-gray-100 hover:bg-primary hover:text-white flex items-center justify-center transition-all duration-200">
                                        <?php if($platform === 'linkedin'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                            </svg>
                                        <?php elseif($platform === 'github'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                            </svg>
                                        <?php elseif($platform === 'twitter'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                            </svg>
                                        <?php elseif($platform === 'instagram'): ?>
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                            </svg>
                                        <?php endif; ?>
                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Tab -->
    <div id="courses-content" class="tab-content hidden">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Semua Kursus dari <?php echo e($profile->public_name); ?></h2>

            <?php if($courses->count() > 0): ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="content-card rounded-lg overflow-hidden">
                            <div class="aspect-video bg-gray-100 relative">
                                <?php if($course->thumbnail): ?>
                                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                                         alt="<?php echo e($course->title); ?>"
                                         class="w-full h-full object-cover">
                                <?php else: ?>
                                    <div class="w-full h-full flex items-center justify-center">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    </div>
                                <?php endif; ?>

                                <!-- Price Badge -->
                                <div class="absolute top-3 right-3">
                                    <?php if($course->is_free): ?>
                                        <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Gratis</span>
                                    <?php else: ?>
                                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">Rp <?php echo e(number_format($course->price, 0, ',', '.')); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="p-6">
                                <div class="flex items-center justify-between mb-3">
                                    <?php if($course->category): ?>
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium"><?php echo e($course->category->name); ?></span>
                                    <?php endif; ?>
                                    <span class="text-sm text-gray-500"><?php echo e($course->level); ?></span>
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($course->title); ?></h3>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($course->description); ?></p>

                                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                    <span><?php echo e($course->total_lessons); ?> pelajaran</span>
                                    <span><?php echo e($course->total_duration_minutes); ?> menit</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400 mr-2">
                                            <?php for($i = 1; $i <= 5; $i++): ?>
                                                <?php if($i <= floor($course->average_rating)): ?>
                                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                    </svg>
                                                <?php else: ?>
                                                    <svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                                    </svg>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                        <span class="text-sm"><?php echo e(number_format($course->average_rating, 1)); ?></span>
                                    </div>

                                    <a href="<?php echo e(route('course.show', $course)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                        Lihat Detail →
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_courses'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('courses.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn bg-primary hover:bg-primary-dark text-white">
                            Lihat Semua Kursus (<?php echo e($stats['total_courses']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Kursus</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat kursus. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Exams Tab -->
    <div id="exams-content" class="tab-content hidden">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Ujian dari <?php echo e($profile->public_name); ?></h2>

            <?php if($exams->count() > 0): ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="content-card rounded-lg overflow-hidden p-6">
                            <div class="flex items-center justify-between mb-4">
                                <?php if($exam->category): ?>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium"><?php echo e($exam->category->name); ?></span>
                                <?php endif; ?>
                                <span class="bg-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-100 text-<?php echo e($exam->difficulty_level === 'beginner' ? 'green' : ($exam->difficulty_level === 'intermediate' ? 'yellow' : 'red')); ?>-800 px-2 py-1 rounded text-xs font-medium">
                                    <?php echo e(ucfirst($exam->difficulty_level)); ?>

                                </span>
                            </div>

                            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo e($exam->title); ?></h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($exam->description); ?></p>

                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e($exam->time_limit); ?> menit
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <?php echo e($exam->total_questions); ?> soal
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Passing score: <?php echo e($exam->passing_score); ?>%
                                </div>
                            </div>

                            <div class="flex items-center justify-between">
                                <div class="text-lg font-bold text-primary">
                                    <?php if($exam->price > 0): ?>
                                        Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?>

                                    <?php else: ?>
                                        Gratis
                                    <?php endif; ?>
                                </div>

                                <a href="<?php echo e(route('exams.show', $exam)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                    Lihat Detail →
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_exams'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('exams.index')); ?>?tutor=<?php echo e($profile->user->id); ?>" class="btn bg-primary hover:bg-primary-dark text-white">
                            Lihat Semua Ujian (<?php echo e($stats['total_exams']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Ujian</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum membuat ujian. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Blogs Tab -->
    <div id="blogs-content" class="tab-content hidden">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Blog dari <?php echo e($profile->public_name); ?></h2>

            <?php if($blogPosts->count() > 0): ?>
                <div class="grid md:grid-cols-2 gap-6">
                    <?php $__currentLoopData = $blogPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="content-card rounded-lg overflow-hidden">
                            <?php if($post->featured_image): ?>
                                <div class="aspect-video bg-gray-100">
                                    <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>"
                                         alt="<?php echo e($post->title); ?>"
                                         class="w-full h-full object-cover">
                                </div>
                            <?php endif; ?>

                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-3">
                                    <?php if($post->category): ?>
                                        <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs mr-3"><?php echo e($post->category->name); ?></span>
                                    <?php endif; ?>
                                    <time datetime="<?php echo e($post->published_at->format('Y-m-d')); ?>">
                                        <?php echo e($post->published_at->format('d M Y')); ?>

                                    </time>
                                    <span class="mx-2">•</span>
                                    <span><?php echo e($post->read_time); ?> menit baca</span>
                                </div>

                                <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">
                                    <a href="<?php echo e(route('blog.show', $post)); ?>" class="hover:text-primary transition-colors">
                                        <?php echo e($post->title); ?>

                                    </a>
                                </h3>

                                <p class="text-gray-600 text-sm mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-sm text-gray-500">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <?php echo e($post->views_count); ?> views
                                    </div>

                                    <a href="<?php echo e(route('blog.show', $post)); ?>" class="text-primary hover:text-primary-dark font-medium text-sm">
                                        Baca Selengkapnya →
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($stats['total_blog_posts'] > 6): ?>
                    <div class="text-center mt-8">
                        <a href="<?php echo e(route('blog.index')); ?>?author=<?php echo e($profile->user->id); ?>" class="btn bg-primary hover:bg-primary-dark text-white">
                            Lihat Semua Blog (<?php echo e($stats['total_blog_posts']); ?>)
                        </a>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Blog</h3>
                    <p class="text-gray-500"><?php echo e($profile->public_name); ?> belum menulis blog. Pantau terus untuk update terbaru!</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Call to Action Section -->
<section class="bg-gradient-to-r from-primary to-orange-500 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Siap Belajar dengan <?php echo e($profile->public_name); ?>?</h2>
        <p class="text-xl text-white/90 mb-8">Bergabung dengan <?php echo e($stats['total_students']); ?> siswa lainnya dan tingkatkan skill Anda hari ini!</p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="https://wa.me/<?php echo e(preg_replace('/[^0-9]/', '', $profile->phone_number)); ?>"
               target="_blank"
               class="btn bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg font-semibold">
                <svg class="w-5 h-5 mr-2 inline" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
                Hubungi Sekarang
            </a>

            <?php if($courses->count() > 0): ?>
                <button onclick="showTab('courses')" class="btn bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 text-lg font-semibold">
                    Lihat Kursus
                </button>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Back to Home -->
<div class="bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <a href="<?php echo e(route('home')); ?>"
           class="text-primary hover:text-primary-dark font-medium">
            ← Kembali ke Beranda Ngambiskuy
        </a>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.add('hidden');
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Show selected tab content
    const selectedContent = document.getElementById(tabName + '-content');
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
    }

    // Add active class to selected tab button
    const selectedButton = document.getElementById(tabName + '-tab');
    if (selectedButton) {
        selectedButton.classList.add('active');
    }

    // Update URL hash without scrolling
    if (history.pushState) {
        history.pushState(null, null, '#' + tabName);
    } else {
        location.hash = '#' + tabName;
    }
}

// Initialize the first tab as active on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL
    const hash = window.location.hash.substring(1);
    const validTabs = ['overview', 'courses', 'exams', 'blogs'];

    if (hash && validTabs.includes(hash)) {
        showTab(hash);
    } else {
        showTab('overview');
    }

    // Handle browser back/forward buttons
    window.addEventListener('popstate', function() {
        const hash = window.location.hash.substring(1);
        if (hash && validTabs.includes(hash)) {
            showTab(hash);
        } else {
            showTab('overview');
        }
    });
});

// Smooth scroll for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Add loading animation for images
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.style.opacity = '1';
        });

        // Add loading placeholder
        if (!img.complete) {
            img.style.opacity = '0.5';
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/tutor/public-profile-v2.blade.php ENDPATH**/ ?>